#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试评论逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from xhs_comment_bot_enhanced import XHSCommentBotEnhanced
from config import CONFIG, COMMENT_POOL

def test_comment_logic():
    """测试评论逻辑"""
    print("=== 测试评论逻辑 ===")
    print(f"评论池: {COMMENT_POOL}")
    print(f"默认评论: '{CONFIG['task']['default_comment']}'")
    print(f"默认评论是否是评论池第一个元素: {CONFIG['task']['default_comment'] == COMMENT_POOL[0]}")
    print()
    
    bot = XHSCommentBotEnhanced()
    
    # 模拟不使用随机评论的情况
    print("=== 模拟不使用随机评论 (use_random=False) ===")
    use_random = False
    comment_text = None
    
    # 这是 run_comment_task 中的逻辑
    if not comment_text:
        comment_text = bot.get_random_comment() if use_random else CONFIG['task']['default_comment']
    
    print(f"最终评论内容: '{comment_text}'")
    print()
    
    # 模拟使用随机评论的情况
    print("=== 模拟使用随机评论 (use_random=True) ===")
    use_random = True
    comment_text = None
    
    # 这是 run_comment_task 中的逻辑
    if not comment_text:
        comment_text = bot.get_random_comment() if use_random else CONFIG['task']['default_comment']
    
    print(f"初始评论内容: '{comment_text}'")
    
    # 在循环中，每个帖子都会重新生成评论
    for i in range(5):
        current_comment = bot.get_random_comment() if use_random else comment_text
        print(f"帖子 {i+1} 的评论: '{current_comment}'")

if __name__ == "__main__":
    test_comment_logic()
