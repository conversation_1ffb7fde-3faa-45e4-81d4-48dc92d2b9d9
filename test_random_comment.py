#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试随机评论功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from xhs_comment_bot_enhanced import XHSCommentBotEnhanced
from config import COMMENT_POOL
import random
from collections import Counter

def test_random_comment():
    """测试随机评论功能"""
    print("=== 测试随机评论功能 ===")
    print(f"评论池内容: {COMMENT_POOL}")
    print(f"评论池大小: {len(COMMENT_POOL)}")
    print(f"第一个元素: '{COMMENT_POOL[0]}'")
    print()

    # 创建机器人实例
    bot = XHSCommentBotEnhanced()

    # 测试多次调用
    test_count = 50  # 增加测试次数
    results = []

    print(f"进行 {test_count} 次随机选择测试:")
    for i in range(test_count):
        comment = bot.get_random_comment()
        results.append(comment)
        print(f"第 {i+1:2d} 次: {comment}")

    print()
    print("=== 统计结果 ===")
    counter = Counter(results)
    for comment in COMMENT_POOL:  # 按原始顺序显示
        count = counter.get(comment, 0)
        percentage = (count / test_count) * 100
        print(f"'{comment}': {count} 次 ({percentage:.1f}%)")

    print()
    print("=== 直接测试 random.choice ===")
    direct_results = []
    for i in range(test_count):
        comment = random.choice(COMMENT_POOL)
        direct_results.append(comment)
        print(f"第 {i+1:2d} 次: {comment}")

    print()
    print("=== 直接测试统计结果 ===")
    direct_counter = Counter(direct_results)
    for comment in COMMENT_POOL:  # 按原始顺序显示
        count = direct_counter.get(comment, 0)
        percentage = (count / test_count) * 100
        print(f"'{comment}': {count} 次 ({percentage:.1f}%)")

    # 检查是否所有结果都是第一个元素
    first_element = COMMENT_POOL[0]
    all_first_bot = all(result == first_element for result in results)
    all_first_direct = all(result == first_element for result in direct_results)

    # 检查第一个元素的出现次数
    first_count_bot = counter.get(first_element, 0)
    first_count_direct = direct_counter.get(first_element, 0)

    print()
    print("=== 分析结果 ===")
    print(f"机器人方法是否总是返回第一个元素: {'是' if all_first_bot else '否'}")
    print(f"直接调用是否总是返回第一个元素: {'是' if all_first_direct else '否'}")
    print(f"机器人方法中第一个元素出现次数: {first_count_bot}/{test_count} ({first_count_bot/test_count*100:.1f}%)")
    print(f"直接调用中第一个元素出现次数: {first_count_direct}/{test_count} ({first_count_direct/test_count*100:.1f}%)")

    if all_first_bot:
        print("❌ 发现问题：机器人的 get_random_comment() 方法确实总是返回第一个元素")
    elif first_count_bot == 0:
        print("⚠️  奇怪：机器人的 get_random_comment() 方法从未返回第一个元素")
    else:
        print("✅ 机器人的 get_random_comment() 方法工作正常")

    if all_first_direct:
        print("❌ 发现问题：random.choice() 本身有问题")
    else:
        print("✅ random.choice() 工作正常")

if __name__ == "__main__":
    test_random_comment()
